/**
 * 博客页面Ajax无感分页功能
 * 实现点击分页链接时不刷新页面，通过Ajax获取数据并更新DOM
 */

(function() {
    'use strict';

    // 分页配置
    const PAGINATION_CONFIG = {
        containerSelector: '#blog-table-container',
        paginationSelector: '.pager_box',
        ajaxUrl: '/Blog?handler=AjaxPage',
        loadingClass: 'ajax-loading'
    };

    // 当前分页状态
    let currentState = {
        currentPage: 1,
        totalPages: 1,
        totalCount: 0,
        pageSize: 10,
        filters: {
            keyword: '',
            status: '',
            cateId: '',
            publishTime: '',
            tagId: ''
        }
    };

    // 防止重复请求的标志
    let isLoading = false;

    /**
     * 初始化Ajax分页功能
     */
    function initAjaxPagination() {
        // 从页面获取初始状态
        updateStateFromPage();

        // 绑定分页链接点击事件
        bindPaginationEvents();

        // 绑定页码跳转事件
        bindJumpPageEvents();

        console.log('博客Ajax分页功能已初始化，当前页:', currentState.currentPage);
    }

    /**
     * 从页面元素获取当前状态
     */
    function updateStateFromPage() {
        // 从分页信息中获取状态
        const paginationInfo = document.querySelector('.pager .info');
        if (paginationInfo) {
            const infoText = paginationInfo.textContent;
            const matches = infoText.match(/共\s*(\d+)\s*条记录，(\d+)\s*页/);
            if (matches) {
                currentState.totalCount = parseInt(matches[1]);
                currentState.totalPages = parseInt(matches[2]);
            }
        }

        // 从URL参数获取当前页码（更可靠）
        const urlParams = new URLSearchParams(window.location.search);
        const currentPageFromUrl = urlParams.get('CurrentPage');
        if (currentPageFromUrl) {
            currentState.currentPage = parseInt(currentPageFromUrl) || 1;
        } else {
            // 如果URL中没有页码参数，尝试从页面元素获取
            const currentPageElement = document.querySelector('.pager .current');
            if (currentPageElement) {
                currentState.currentPage = parseInt(currentPageElement.textContent) || 1;
            } else {
                currentState.currentPage = 1; // 默认第一页
            }
        }

        // 获取筛选条件
        currentState.filters = {
            keyword: urlParams.get('keyword') || '',
            status: urlParams.get('status') || '',
            cateId: urlParams.get('cateId') || '',
            publishTime: urlParams.get('publishTime') || '',
            tagId: urlParams.get('tagId') || ''
        };

        // 获取页面大小
        const pageSizeFromUrl = urlParams.get('PageSize');
        if (pageSizeFromUrl) {
            currentState.pageSize = parseInt(pageSizeFromUrl);
        }


    }

    /**
     * 绑定分页链接点击事件
     */
    function bindPaginationEvents() {
        // 只在博客列表页面的分页容器内监听事件，避免与其他分页脚本冲突
        const paginationContainer = document.querySelector(PAGINATION_CONFIG.paginationSelector);
        if (paginationContainer) {
            paginationContainer.addEventListener('click', function(e) {
                const target = e.target;

                // 检查是否是分页链接
                if (target.matches('.pager a') || target.closest('.pager a')) {
                    e.preventDefault();
                    e.stopPropagation();

                    // 防止重复请求
                    if (isLoading) {
                        return false;
                    }

                    const link = target.matches('.pager a') ? target : target.closest('.pager a');
                    const href = link.getAttribute('href');
                    const dataPage = link.getAttribute('data-page');

                    let targetPage = null;

                    // 优先使用data-page属性
                    if (dataPage) {
                        targetPage = parseInt(dataPage);
                    } else if (href && href !== '#' && href !== 'javascript:;') {
                        // 从链接中解析页码
                        const pageMatch = href.match(/[?&]CurrentPage=(\d+)/);
                        if (pageMatch) {
                            targetPage = parseInt(pageMatch[1]);
                        }
                    }

                    if (targetPage) {
                        if (targetPage === currentState.currentPage) {
                            return false;
                        }
                        loadPage(targetPage);
                    }

                    return false;
                }
            });

        }
    }

    /**
     * 绑定页码跳转事件
     */
    function bindJumpPageEvents() {
        // 只在博客列表页面的分页容器内监听跳转事件
        const paginationContainer = document.querySelector(PAGINATION_CONFIG.paginationSelector);
        if (paginationContainer) {
            paginationContainer.addEventListener('click', handleJumpPageClick);
            paginationContainer.addEventListener('keypress', handleJumpPageKeypress);
        }
    }

    /**
     * 处理跳转按钮点击事件
     */
    function handleJumpPageClick(e) {
        if (e.target.matches('#jump-page-btn')) {
            e.preventDefault();
            e.stopPropagation();

            // 防止重复请求
            if (isLoading) {
                return false;
            }

            const pageInput = document.getElementById('jump-page-input');
            if (pageInput) {
                const pageNum = parseInt(pageInput.value);
                const maxPage = parseInt(pageInput.getAttribute('max')) || currentState.totalPages;

                // 验证页码
                let targetPage = pageNum;
                if (isNaN(targetPage) || targetPage < 1) {
                    targetPage = 1;
                } else if (targetPage > maxPage) {
                    targetPage = maxPage;
                }

                if (targetPage !== currentState.currentPage) {
                    loadPage(targetPage);
                }
            }

            return false;
        }
    }

    /**
     * 处理跳转输入框回车事件
     */
    function handleJumpPageKeypress(e) {
        if (e.target.matches('#jump-page-input') && e.which === 13) {
            e.preventDefault();
            document.getElementById('jump-page-btn')?.click();
        }
    }

    /**
     * 加载指定页码的数据
     * @param {number} pageNum 目标页码
     */
    function loadPage(pageNum) {
        if (pageNum === currentState.currentPage) {
            return; // 如果是当前页，不需要重新加载
        }

        // 防止重复请求
        if (isLoading) {
            return;
        }

        // 设置加载标志
        isLoading = true;

        // 显示加载状态
        showLoading();

        // 构建请求参数
        const params = new URLSearchParams();
        params.set('PageIndex', pageNum);
        params.set('PageSize', currentState.pageSize);
        
        // 添加筛选条件
        Object.keys(currentState.filters).forEach(key => {
            const value = currentState.filters[key];
            if (value) {
                params.set(key, value);
            }
        });

        // 发送Ajax请求获取分页信息
        const ajaxUrl = `${PAGINATION_CONFIG.ajaxUrl}&${params.toString()}`;

        fetch(ajaxUrl, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                console.log('分页信息获取成功:', data);

                // 更新状态
                currentState.currentPage = data.pagination.currentPage;
                currentState.totalPages = data.pagination.totalPages;
                currentState.totalCount = data.pagination.totalCount;
                currentState.pageSize = data.pagination.pageSize;
                currentState.filters = data.filters;

                // 获取HTML内容
                return fetchTableHtml(params);
            } else {
                throw new Error(data.message || '加载数据失败');
            }
        })
        .then(htmlContent => {
            console.log('HTML内容获取成功，开始更新页面');

            // 更新页面内容 - 使用目标页码pageNum而不是currentState.currentPage
            updatePageContent({ html: htmlContent, pagination: {
                currentPage: pageNum,
                totalPages: currentState.totalPages,
                totalCount: currentState.totalCount,
                pageSize: currentState.pageSize
            }});

            // 更新浏览器URL
            updateBrowserUrl(pageNum);

            console.log(`已加载第 ${pageNum} 页数据`);
        })
        .catch(error => {
            console.error('Ajax分页请求失败:', error);
            showError('加载数据失败，请稍后重试');
        })
        .finally(() => {
            hideLoading();
            // 重置加载标志
            isLoading = false;
        });
    }

    /**
     * 获取表格HTML内容
     * @param {URLSearchParams} params 请求参数
     * @returns {Promise<string>} HTML内容
     */
    async function fetchTableHtml(params) {
        try {
            const refreshUrl = '/Blog?handler=RefreshTable&' + params.toString();
            console.log('正在获取HTML内容:', refreshUrl);

            const response = await fetch(refreshUrl, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            console.log('HTML请求响应状态:', response.status);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const htmlContent = await response.text();
            console.log('获取到HTML内容长度:', htmlContent.length);

            return htmlContent;
        } catch (error) {
            console.error('获取HTML内容失败:', error);
            throw error;
        }
    }

    /**
     * 更新页面内容
     * @param {Object} data 服务器返回的数据
     */
    function updatePageContent(data) {
        // 更新表格内容
        const container = document.querySelector(PAGINATION_CONFIG.containerSelector);
        if (container && data.html) {
            container.innerHTML = data.html;
        }

        // 更新当前状态
        if (data.pagination) {
            currentState.currentPage = data.pagination.currentPage;
            currentState.totalPages = data.pagination.totalPages;
            currentState.totalCount = data.pagination.totalCount;
            currentState.pageSize = data.pagination.pageSize;
        }

        // 重新生成分页控件
        generatePaginationControls(data.pagination);

        // 重新初始化表格相关功能（如果需要）
        reinitializeTableFeatures();
    }

    /**
     * 生成分页控件HTML
     * @param {Object} pagination 分页信息
     */
    function generatePaginationControls(pagination) {
        const paginationContainer = document.querySelector(PAGINATION_CONFIG.paginationSelector);
        if (!paginationContainer || pagination.totalPages <= 0) {
            console.log('分页容器不存在或总页数为0');
            return;
        }

        const { currentPage, totalPages, totalCount, pageSize } = pagination;
        console.log('生成分页控件，当前页:', currentPage, '总页数:', totalPages);
        
        // 计算分页显示逻辑
        let startPage = 1;
        let endPage = totalPages;
        let showStartEllipsis = false;
        let showEndEllipsis = false;

        if (totalPages > 7) {
            if (currentPage <= 4) {
                startPage = 1;
                endPage = 5;
                showEndEllipsis = true;
            } else if (currentPage >= totalPages - 3) {
                startPage = totalPages - 4;
                endPage = totalPages;
                showStartEllipsis = true;
            } else {
                startPage = currentPage - 2;
                endPage = currentPage + 2;
                showStartEllipsis = true;
                showEndEllipsis = true;
            }
        }

        // 构建分页HTML
        let paginationHtml = '<div class="pager">';

        // 上一页
        if (currentPage > 1) {
            paginationHtml += `<a href="#" data-page="${currentPage - 1}" class="prev">上一页</a>`;
        }

        // 第一页和省略号
        if (showStartEllipsis) {
            if (currentPage === 1) {
                paginationHtml += '<span class="current">1</span>';
            } else {
                paginationHtml += '<a href="#" data-page="1">1</a>';
            }
            paginationHtml += '<span class="ellipsis">...</span>';
        }

        // 页码范围
        for (let i = startPage; i <= endPage; i++) {
            if (i === currentPage) {
                paginationHtml += `<span class="current">${i}</span>`;
            } else {
                paginationHtml += `<a href="#" data-page="${i}">${i}</a>`;
            }
        }

        // 最后一页和省略号
        if (showEndEllipsis) {
            paginationHtml += '<span class="ellipsis">...</span>';
            if (currentPage === totalPages) {
                paginationHtml += `<span class="current">${totalPages}</span>`;
            } else {
                paginationHtml += `<a href="#" data-page="${totalPages}">${totalPages}</a>`;
            }
        }

        // 下一页
        if (currentPage < totalPages) {
            paginationHtml += `<a href="#" data-page="${currentPage + 1}" class="next">下一页</a>`;
        }

        // 页码跳转
        paginationHtml += `
            <span class="page-jump">
                跳转到
                <input type="number" id="jump-page-input" min="1" max="${totalPages}" value="${currentPage}" style="width: 50px; text-align: center; margin: 0 5px;">
                页
                <button type="button" id="jump-page-btn" style="margin-left: 5px; padding: 4px 8px;">跳转</button>
            </span>
        `;

        // 统计信息
        paginationHtml += `<span class="info">共 ${totalCount} 条记录，${totalPages} 页</span>`;
        paginationHtml += '</div>';

        paginationContainer.innerHTML = paginationHtml;

        console.log('分页控件HTML已更新，当前页:', currentPage);

        // 不需要重复绑定事件，因为已经在容器上绑定了事件委托
    }





    /**
     * 更新浏览器URL（不刷新页面）
     * @param {number} pageNum 页码
     */
    function updateBrowserUrl(pageNum) {
        const url = new URL(window.location);
        url.searchParams.set('CurrentPage', pageNum);
        
        // 保持其他筛选参数
        Object.keys(currentState.filters).forEach(key => {
            const value = currentState.filters[key];
            if (value) {
                url.searchParams.set(key, value);
            } else {
                url.searchParams.delete(key);
            }
        });

        // 使用pushState更新URL，不刷新页面
        window.history.pushState({ page: pageNum }, '', url.toString());
    }

    /**
     * 显示加载状态
     */
    function showLoading() {
        const container = document.querySelector(PAGINATION_CONFIG.containerSelector);
        if (container) {
            container.classList.add(PAGINATION_CONFIG.loadingClass);
            
            // 添加加载遮罩
            const loadingOverlay = document.createElement('div');
            loadingOverlay.className = 'ajax-loading-overlay';
            loadingOverlay.innerHTML = '<div class="loading-spinner">加载中...</div>';
            container.style.position = 'relative';
            container.appendChild(loadingOverlay);
        }
    }

    /**
     * 隐藏加载状态
     */
    function hideLoading() {
        console.log('开始隐藏加载状态');
        const container = document.querySelector(PAGINATION_CONFIG.containerSelector);
        if (container) {
            container.classList.remove(PAGINATION_CONFIG.loadingClass);

            // 移除加载遮罩
            const loadingOverlay = container.querySelector('.ajax-loading-overlay');
            if (loadingOverlay) {
                loadingOverlay.remove();
                console.log('加载遮罩已移除');
            } else {
                console.log('未找到加载遮罩');
            }

            console.log('加载状态已隐藏');
        } else {
            console.log('未找到容器元素');
        }
    }

    /**
     * 显示错误信息
     * @param {string} message 错误信息
     */
    function showError(message) {
        // 这里可以使用项目中的通知组件
        console.error(message);
        alert(message); // 临时使用alert，可以替换为更好的UI组件
    }

    /**
     * 更新当前页的样式
     * @param {number} currentPage 当前页码
     */
    function updateCurrentPageStyle(currentPage) {
        // 查找所有分页元素
        const allPageElements = document.querySelectorAll('.pager a, .pager span');

        allPageElements.forEach(element => {
            const elementText = element.textContent.trim();

            if (elementText === currentPage.toString()) {
                // 这是当前页，设置为选中状态
                element.classList.add('current');
                element.style.backgroundColor = '#007bff';
                element.style.color = 'white';
                element.style.borderColor = '#007bff';

                // 如果是链接，转换为span
                if (element.tagName === 'A') {
                    const span = document.createElement('span');
                    span.className = 'current';
                    span.textContent = elementText;
                    span.style.backgroundColor = '#007bff';
                    span.style.color = 'white';
                    span.style.borderColor = '#007bff';
                    element.parentNode.replaceChild(span, element);
                }
            } else {
                // 不是当前页，移除选中状态
                element.classList.remove('current');
                element.style.backgroundColor = '';
                element.style.color = '';
                element.style.borderColor = '';
            }
        });

        console.log('当前页样式已更新:', currentPage);
    }

    /**
     * 重新初始化表格相关功能
     */
    function reinitializeTableFeatures() {
        // 重新初始化博客相关功能
        if (typeof blog_obj !== 'undefined' && blog_obj.blog_init) {
            blog_obj.blog_init();
        }

        // 重新初始化选择框功能
        if (typeof updateSelectedCount === 'function') {
            updateSelectedCount();
        }

        // 重新绑定其他事件（如果需要）
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initAjaxPagination);
    } else {
        initAjaxPagination();
    }

    // 处理浏览器前进后退
    window.addEventListener('popstate', function(e) {
        if (e.state && e.state.page) {
            loadPage(e.state.page);
        } else {
            // 如果没有状态信息，重新加载页面
            window.location.reload();
        }
    });

    // 导出到全局作用域（如果需要）
    window.BlogAjaxPagination = {
        loadPage: loadPage,
        getCurrentState: () => ({ ...currentState })
    };

})();
